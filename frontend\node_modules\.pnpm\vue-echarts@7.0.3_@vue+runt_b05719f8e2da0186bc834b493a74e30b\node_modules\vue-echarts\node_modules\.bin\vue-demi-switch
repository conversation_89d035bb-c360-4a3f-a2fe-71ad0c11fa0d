#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/桌面/news-classification-system/frontend/node_modules/.pnpm/vue-demi@0.13.11_vue@3.5.17_typescript@5.8.3_/node_modules/vue-demi/bin/node_modules:/mnt/d/桌面/news-classification-system/frontend/node_modules/.pnpm/vue-demi@0.13.11_vue@3.5.17_typescript@5.8.3_/node_modules/vue-demi/node_modules:/mnt/d/桌面/news-classification-system/frontend/node_modules/.pnpm/vue-demi@0.13.11_vue@3.5.17_typescript@5.8.3_/node_modules:/mnt/d/桌面/news-classification-system/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/桌面/news-classification-system/frontend/node_modules/.pnpm/vue-demi@0.13.11_vue@3.5.17_typescript@5.8.3_/node_modules/vue-demi/bin/node_modules:/mnt/d/桌面/news-classification-system/frontend/node_modules/.pnpm/vue-demi@0.13.11_vue@3.5.17_typescript@5.8.3_/node_modules/vue-demi/node_modules:/mnt/d/桌面/news-classification-system/frontend/node_modules/.pnpm/vue-demi@0.13.11_vue@3.5.17_typescript@5.8.3_/node_modules:/mnt/d/桌面/news-classification-system/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../vue-demi@0.13.11_vue@3.5.17_typescript@5.8.3_/node_modules/vue-demi/bin/vue-demi-switch.js" "$@"
else
  exec node  "$basedir/../../../../../vue-demi@0.13.11_vue@3.5.17_typescript@5.8.3_/node_modules/vue-demi/bin/vue-demi-switch.js" "$@"
fi
