{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.10.0", "echarts": "^5.6.0", "element-plus": "^2.10.2", "vue": "^3.5.17", "vue-echarts": "^7.0.3", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^7.0.0", "vue-tsc": "^2.2.10"}}