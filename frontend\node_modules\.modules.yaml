hoistPattern:
  - '*'
hoistedDependencies:
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/parser@7.27.7':
    '@babel/parser': private
  '@babel/types@7.27.7':
    '@babel/types': private
  '@ctrl/tinycolor@3.6.1':
    '@ctrl/tinycolor': private
  '@esbuild/win32-x64@0.25.5':
    '@esbuild/win32-x64': private
  '@floating-ui/core@1.7.2':
    '@floating-ui/core': private
  '@floating-ui/dom@1.7.2':
    '@floating-ui/dom': private
  '@floating-ui/utils@0.2.10':
    '@floating-ui/utils': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@rolldown/pluginutils@1.0.0-beta.19':
    '@rolldown/pluginutils': private
  '@rollup/rollup-win32-x64-msvc@4.44.1':
    '@rollup/rollup-win32-x64-msvc': private
  '@sxzz/popperjs-es@2.11.7':
    '@popperjs/core': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/lodash-es@4.17.12':
    '@types/lodash-es': private
  '@types/lodash@4.17.19':
    '@types/lodash': private
  '@types/web-bluetooth@0.0.16':
    '@types/web-bluetooth': private
  '@volar/language-core@2.4.15':
    '@volar/language-core': private
  '@volar/source-map@2.4.15':
    '@volar/source-map': private
  '@volar/typescript@2.4.15':
    '@volar/typescript': private
  '@vue/compiler-core@3.5.17':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.5.17':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.5.17':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.5.17':
    '@vue/compiler-ssr': private
  '@vue/compiler-vue2@2.7.16':
    '@vue/compiler-vue2': private
  '@vue/devtools-api@6.6.4':
    '@vue/devtools-api': private
  '@vue/language-core@2.2.10(typescript@5.8.3)':
    '@vue/language-core': private
  '@vue/reactivity@3.5.17':
    '@vue/reactivity': private
  '@vue/runtime-core@3.5.17':
    '@vue/runtime-core': private
  '@vue/runtime-dom@3.5.17':
    '@vue/runtime-dom': private
  '@vue/server-renderer@3.5.17(vue@3.5.17(typescript@5.8.3))':
    '@vue/server-renderer': private
  '@vue/shared@3.5.17':
    '@vue/shared': private
  '@vueuse/core@9.13.0(vue@3.5.17(typescript@5.8.3))':
    '@vueuse/core': private
  '@vueuse/metadata@9.13.0':
    '@vueuse/metadata': private
  '@vueuse/shared@9.13.0(vue@3.5.17(typescript@5.8.3))':
    '@vueuse/shared': private
  alien-signals@1.0.13:
    alien-signals: private
  async-validator@4.2.5:
    async-validator: private
  asynckit@0.4.0:
    asynckit: private
  balanced-match@1.0.2:
    balanced-match: private
  brace-expansion@2.0.2:
    brace-expansion: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  combined-stream@1.0.8:
    combined-stream: private
  csstype@3.1.3:
    csstype: private
  dayjs@1.11.13:
    dayjs: private
  de-indent@1.0.2:
    de-indent: private
  delayed-stream@1.0.0:
    delayed-stream: private
  dunder-proto@1.0.1:
    dunder-proto: private
  entities@4.5.0:
    entities: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  esbuild@0.25.5:
    esbuild: private
  escape-html@1.0.3:
    escape-html: private
  estree-walker@2.0.2:
    estree-walker: private
  fdir@6.4.6(picomatch@4.0.2):
    fdir: private
  follow-redirects@1.15.9:
    follow-redirects: private
  form-data@4.0.3:
    form-data: private
  function-bind@1.1.2:
    function-bind: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  gopd@1.2.0:
    gopd: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  he@1.2.0:
    he: private
  lodash-es@4.17.21:
    lodash-es: private
  lodash-unified@1.0.3(@types/lodash-es@4.17.12)(lodash-es@4.17.21)(lodash@4.17.21):
    lodash-unified: private
  lodash@4.17.21:
    lodash: private
  magic-string@0.30.17:
    magic-string: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  memoize-one@6.0.0:
    memoize-one: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  minimatch@9.0.5:
    minimatch: private
  muggle-string@0.4.1:
    muggle-string: private
  nanoid@3.3.11:
    nanoid: private
  normalize-wheel-es@1.2.0:
    normalize-wheel-es: private
  path-browserify@1.0.1:
    path-browserify: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  postcss@8.5.6:
    postcss: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  rollup@4.44.1:
    rollup: private
  source-map-js@1.2.1:
    source-map-js: private
  tinyglobby@0.2.14:
    tinyglobby: private
  tslib@2.3.0:
    tslib: private
  vscode-uri@3.1.0:
    vscode-uri: private
  vue-demi@0.13.11(vue@3.5.17(typescript@5.8.3)):
    vue-demi: private
  zrender@5.6.1:
    zrender: private
ignoredBuilds:
  - vue-demi
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.4
pendingBuilds: []
prunedAt: Sat, 28 Jun 2025 04:13:31 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.5'
  - '@esbuild/android-arm64@0.25.5'
  - '@esbuild/android-arm@0.25.5'
  - '@esbuild/android-x64@0.25.5'
  - '@esbuild/darwin-arm64@0.25.5'
  - '@esbuild/darwin-x64@0.25.5'
  - '@esbuild/freebsd-arm64@0.25.5'
  - '@esbuild/freebsd-x64@0.25.5'
  - '@esbuild/linux-arm64@0.25.5'
  - '@esbuild/linux-arm@0.25.5'
  - '@esbuild/linux-ia32@0.25.5'
  - '@esbuild/linux-loong64@0.25.5'
  - '@esbuild/linux-mips64el@0.25.5'
  - '@esbuild/linux-ppc64@0.25.5'
  - '@esbuild/linux-riscv64@0.25.5'
  - '@esbuild/linux-s390x@0.25.5'
  - '@esbuild/linux-x64@0.25.5'
  - '@esbuild/netbsd-arm64@0.25.5'
  - '@esbuild/netbsd-x64@0.25.5'
  - '@esbuild/openbsd-arm64@0.25.5'
  - '@esbuild/openbsd-x64@0.25.5'
  - '@esbuild/sunos-x64@0.25.5'
  - '@esbuild/win32-arm64@0.25.5'
  - '@esbuild/win32-ia32@0.25.5'
  - '@rollup/rollup-android-arm-eabi@4.44.1'
  - '@rollup/rollup-android-arm64@4.44.1'
  - '@rollup/rollup-darwin-arm64@4.44.1'
  - '@rollup/rollup-darwin-x64@4.44.1'
  - '@rollup/rollup-freebsd-arm64@4.44.1'
  - '@rollup/rollup-freebsd-x64@4.44.1'
  - '@rollup/rollup-linux-arm-gnueabihf@4.44.1'
  - '@rollup/rollup-linux-arm-musleabihf@4.44.1'
  - '@rollup/rollup-linux-arm64-gnu@4.44.1'
  - '@rollup/rollup-linux-arm64-musl@4.44.1'
  - '@rollup/rollup-linux-loongarch64-gnu@4.44.1'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.44.1'
  - '@rollup/rollup-linux-riscv64-gnu@4.44.1'
  - '@rollup/rollup-linux-riscv64-musl@4.44.1'
  - '@rollup/rollup-linux-s390x-gnu@4.44.1'
  - '@rollup/rollup-linux-x64-gnu@4.44.1'
  - '@rollup/rollup-linux-x64-musl@4.44.1'
  - '@rollup/rollup-win32-arm64-msvc@4.44.1'
  - '@rollup/rollup-win32-ia32-msvc@4.44.1'
  - fsevents@2.3.3
storeDir: D:\.pnpm-store\v10
virtualStoreDir: D:\桌面\news-classification-system\frontend\node_modules\.pnpm
virtualStoreDirMaxLength: 60
